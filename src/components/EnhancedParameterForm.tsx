"use client";

import { useForm, Controller } from "react-hook-form";
import { useEffect } from "react";
import { formatDate } from "@/lib/utils";
import type { Strategy } from "@/types/fund";

interface FormData {
  [key: string]: any;
}

interface EnhancedParameterFormProps {
  strategy: Strategy;
  parameters: Record<string, any>;
  onParametersChange: (parameters: Record<string, any>) => void;
}

export default function EnhancedParameterForm({
  strategy,
  parameters,
  onParametersChange,
}: EnhancedParameterFormProps) {
  const {
    control,
    handleSubmit,
    watch,
    formState: { errors, isValid },
    reset,
    setValue,
  } = useForm<FormData>({
    mode: "onChange",
    defaultValues: parameters,
  });

  // 监听表单变化并实时更新父组件
  const watchedValues = watch();
  
  useEffect(() => {
    if (isValid) {
      onParametersChange(watchedValues);
    }
  }, [watchedValues, isValid, onParametersChange]);

  // 当策略变化时重置表单
  useEffect(() => {
    const initialParams: Record<string, any> = {};
    Object.entries(strategy.parameterSchema).forEach(([key, param]) => {
      initialParams[key] = parameters[key] ?? param.defaultValue ?? "";
    });
    reset(initialParams);
  }, [strategy.id, reset, parameters]);

  // 获取验证规则
  const getValidationRules = (param: Strategy["parameterSchema"][string]) => {
    const rules: any = {};

    if (param.required) {
      rules.required = `${param.label}是必填项`;
    }

    if (param.type === "number") {
      rules.validate = (value: any) => {
        if (value === "" || value === undefined) return true;
        const numValue = Number(value);
        if (isNaN(numValue)) return `${param.label}必须是数字`;
        if (param.min !== undefined && numValue < param.min) {
          return `${param.label}不能小于${param.min}`;
        }
        if (param.max !== undefined && numValue > param.max) {
          return `${param.label}不能大于${param.max}`;
        }
        return true;
      };
    }

    if (param.type === "date") {
      rules.validate = (value: any) => {
        if (!value) return true;
        const date = new Date(value);
        if (isNaN(date.getTime())) {
          return `${param.label}格式不正确`;
        }
        return true;
      };
    }

    return rules;
  };

  // 渲染输入组件
  const renderInput = (key: string, param: Strategy["parameterSchema"][string]) => {
    const error = errors[key];
    const baseInputClasses = `w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors ${
      error ? "border-red-300 bg-red-50" : "border-gray-300"
    }`;

    return (
      <Controller
        name={key}
        control={control}
        rules={getValidationRules(param)}
        render={({ field }) => {
          switch (param.type) {
            case "number":
              return (
                <input
                  {...field}
                  type="number"
                  min={param.min}
                  max={param.max}
                  step={param.step}
                  className={baseInputClasses}
                  placeholder={`请输入${param.label}`}
                />
              );

            case "date":
              return (
                <input
                  {...field}
                  type="date"
                  className={baseInputClasses}
                />
              );

            case "select":
              return (
                <select {...field} className={baseInputClasses}>
                  <option value="">请选择{param.label}</option>
                  {param.options?.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              );

            case "range":
              return (
                <div className="space-y-2">
                  <input
                    {...field}
                    type="range"
                    min={param.min}
                    max={param.max}
                    step={param.step}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                  <div className="flex justify-between text-sm text-gray-500">
                    <span>{param.min}</span>
                    <span className="font-medium">{field.value}</span>
                    <span>{param.max}</span>
                  </div>
                </div>
              );

            default:
              return (
                <input
                  {...field}
                  type="text"
                  className={baseInputClasses}
                  placeholder={`请输入${param.label}`}
                />
              );
          }
        }}
      />
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">策略参数设置</h3>
        <div className={`px-3 py-1 rounded-full text-sm font-medium ${
          isValid ? "bg-green-100 text-green-800" : "bg-yellow-100 text-yellow-800"
        }`}>
          {isValid ? "参数有效" : "请检查参数"}
        </div>
      </div>

      <form className="space-y-4">
        {Object.entries(strategy.parameterSchema).map(([key, param]) => (
          <div key={key} className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              {param.label}
              {param.required && (
                <span className="text-red-500 ml-1">*</span>
              )}
            </label>

            {renderInput(key, param)}

            {param.description && (
              <p className="text-sm text-gray-500">{param.description}</p>
            )}

            {errors[key] && (
              <p className="text-sm text-red-600 flex items-center">
                <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                {errors[key]?.message}
              </p>
            )}
          </div>
        ))}
      </form>

      {/* 参数预览 */}
      <div className="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
        <h4 className="font-medium text-gray-900 mb-3">参数预览</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {Object.entries(strategy.parameterSchema).map(([key, param]) => {
            const value = watchedValues[key];
            let displayValue = value;

            // 格式化显示值
            if (param.type === "select" && param.options) {
              const option = param.options.find((opt) => opt.value === value);
              displayValue = option ? option.label : value;
            } else if (param.type === "number" && value) {
              displayValue = Number(value).toLocaleString();
            } else if (param.type === "date" && value) {
              displayValue = formatDate(value, "YYYY年MM月DD日");
            }

            return (
              <div key={key} className="flex justify-between text-sm p-2 bg-white rounded border">
                <span className="text-gray-600">{param.label}:</span>
                <span className="font-medium text-gray-900">
                  {displayValue || "未设置"}
                  {param.type === "number" && value && (
                    <span className="text-gray-500 ml-1">
                      {key.includes("Amount") || key.includes("Investment")
                        ? "元"
                        : key.includes("Rate") || key.includes("Threshold")
                          ? "%"
                          : key.includes("Period")
                            ? "天"
                            : ""}
                    </span>
                  )}
                </span>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
